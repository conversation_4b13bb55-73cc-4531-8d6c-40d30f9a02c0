server.port=9192

spring.datasource.url=*********************************************
spring.datasource.username=root
spring.datasource.password=admin

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.properties.hibernate.dialect= org.hibernate.dialect.MySQLDialect


spring.servlet.multipart.max-file-size=500KB
spring.servlet.multipart.max-request-size=500KB

spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.ddl-auto=update


logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# App Properties

auth.token.expirationInMils=3600000
auth.token.jwtSecret=36763979244226452948404D635166546A576D5A7134743777217A25432A462D
